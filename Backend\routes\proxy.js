const express = require('express');
const { protect } = require('../middleware/auth');
const {
  serveContent,
  servePreview,
  streamContent,
  serveThumbnail,
  serveFile
} = require('../controllers/proxy');

const router = express.Router();

// All proxy routes require authentication
router.use(protect);

// Content proxy routes
router.get('/content/:contentId', serveContent);
router.get('/preview/:contentId', servePreview);
router.get('/stream/:contentId', streamContent);
router.get('/thumbnail/:contentId', serveThumbnail);

// File proxy route for direct file URLs (used during upload preview)
router.get('/file', serveFile);

module.exports = router;
