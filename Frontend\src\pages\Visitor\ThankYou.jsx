import { useEffect, useState } from "react";
import { useNavigate, useLocation } from "react-router-dom";
import { useSelector, useDispatch } from "react-redux";
import { FaDownload, FaSpinner } from "react-icons/fa";
import { MdHome } from "react-icons/md";
import { getOrder } from "../../redux/slices/orderSlice";
import "../../styles/ThankYou.css";
import mastercardLogo from "../../assets/images/cards/mastercard.png";
import visaLogo from "../../assets/images/cards/visa.png";
import amexLogo from "../../assets/images/cards/amex.png";
import discoverLogo from "../../assets/images/cards/discover.png";
import dinersLogo from "../../assets/images/cards/diners.png";
import jcbLogo from "../../assets/images/cards/jcb.png";
import unionpayLogo from "../../assets/images/cards/unionpay.png";
import defaultLogo from "../../assets/images/cards/default.png";
import Thankyou from "../../assets/images/thankyou.svg";
import companyLogo from "../../assets/images/XOsports-hub-logo.png";
import {
  API_BASE_URL,
  getProxyUrlWithAuth,
  getImageUrl,
  getProxyThumbnailUrl,
  getPlaceholderImage
} from "../../utils/constants";
import { jsPDF } from 'jspdf';
import autoTable from 'jspdf-autotable';
import { formatWithTimezone } from '../../utils/timezoneUtils';

// Card logo imports - you may need to add these images to your assets
// For now, we'll use a mapping to the existing mastercard logo as fallback

// Replace the confetti import with dynamic import
const loadConfetti = async () => {
  const confetti = (await import('canvas-confetti')).default;
  return confetti;
};

const ThankYou = () => {
  const navigate = useNavigate();
  const location = useLocation();
  const dispatch = useDispatch();
  const [isDownloading, setIsDownloading] = useState(false);

  // Get order state from Redux
  const { order, downloadUrl } = useSelector((state) => state.order);

  // Helper function to get proper thumbnail URL using proxy system
  const getThumbnailUrl = (thumbnailUrl, contentId) => {
    if (!thumbnailUrl) {
      return getPlaceholderImage(80, 80, "Product");
    }

    // If we have a content ID, use the proxy thumbnail endpoint
    if (contentId) {
      const proxyUrl = getProxyThumbnailUrl(contentId);
      return getProxyUrlWithAuth(proxyUrl);
    }

    // For S3 URLs without content ID, use the file proxy
    if (thumbnailUrl.startsWith('https://') && thumbnailUrl.includes('s3')) {
      return getProxyUrlWithAuth(`${API_BASE_URL}/proxy/file?url=${encodeURIComponent(thumbnailUrl)}`);
    }

    // For local files or other URLs, use the standard getImageUrl
    if (thumbnailUrl.startsWith('http')) {
      return thumbnailUrl;
    }

    return getImageUrl(thumbnailUrl);
  };

  // Helper function to get card logo based on card type
  const getCardLogo = (cardType) => {
    const cardLogos = {
      visa: visaLogo,
      mastercard: mastercardLogo,
      amex: amexLogo,
      discover: discoverLogo,
      diners: dinersLogo,
      jcb: jcbLogo,
      unionpay: unionpayLogo,
      unknown: defaultLogo,
    };
    if (!cardType) return defaultLogo;
    return cardLogos[cardType.toLowerCase()] || defaultLogo;
  };

  // Helper function to format card number display
  const formatCardNumber = (cardType, lastFourDigits) => {
    if (lastFourDigits) {
      return `**** **** **** ${lastFourDigits}`;
    }
    return "**** **** **** 1234"; // Fallback
  };

  // Helper function to get card type display name
  const getCardTypeDisplayName = (cardType) => {
    const cardTypeNames = {
      visa: "Visa",
      mastercard: "Mastercard",
      amex: "American Express",
      discover: "Discover",
      diners: "Diners Club",
      jcb: "JCB",
      unionpay: "UnionPay",
      unknown: "Card",
    };
    if (!cardType) return "Card";
    return cardTypeNames[cardType.toLowerCase()] || "Card";
  };

  // 🎉 Confetti on load + Fetch order if needed
  useEffect(() => {
    window.scrollTo(0, 0);

    // Confetti burst
    const end = Date.now() + 1000;
    const colors = ['#bb0000', '#ffffff', '#00ff00', '#0000ff', '#f1c40f'];

    const fireConfetti = async () => {
      const confetti = await loadConfetti();

      (function frame() {
        confetti({ particleCount: 3, angle: 0, spread: 100, origin: { x: 0.1, y: 0 }, colors });
        confetti({ particleCount: 3, angle: 180, spread: 100, origin: { x: 0.9, y: 0 }, colors });
        if (Date.now() < end) requestAnimationFrame(frame);
      })();
    };

    fireConfetti();

    // Fetch order if coming via query param
    const urlParams = new URLSearchParams(location.search);
    const orderId = urlParams.get("orderId");
    if (orderId && !order) {
      dispatch(getOrder(orderId));
    }
  }, [dispatch, location.search, order]);

  // 🧠 Handle download file from backend URL
  useEffect(() => {
    if (downloadUrl) {
      const link = document.createElement("a");
      link.href = downloadUrl;
      link.download = `order-${orderData.orderId}-content`;
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);
      setIsDownloading(false);
    }
  }, [downloadUrl]);

  // 🧾 Safely prepare orderData (Redux, location, or fallback)
  const getOrderData = () => {
    // Priority: location state > Redux order > mock data
    if (location.state?.orderData) {
      // If coming from checkout page, the data is already properly formatted
      return location.state.orderData;
    }

    if (order) {
      return {
        orderId: `#${order._id?.slice(-8) || "12345678"}`,
        date: formatWithTimezone(order.createdAt || Date.now(), {
          year: 'numeric',
          month: 'long',
          day: 'numeric'
        }),
        time: formatWithTimezone(order.createdAt || Date.now(), {
          hour: '2-digit',
          minute: '2-digit',
          timeZoneName: 'short'
        }),
        items: 1,
        totalAmount: `$${order.amount || "20.00"}`,
        customerDetails: {
          name: order.buyer?.firstName && order.buyer?.lastName
            ? `${order.buyer.firstName} ${order.buyer.lastName}`
            : "John Smith",
          email: order.buyer?.email || "<EMAIL>",
          phone: order.buyer?.mobile || order.buyer?.phone || "Not provided",
        },
        paymentDetails: {
          method:
            getCardTypeDisplayName(order.cardDetails?.cardType) || "Mastercard",
          cardNumber: formatCardNumber(
            order.cardDetails?.cardType,
            order.cardDetails?.lastFourDigits
          ),
          cardType: order.cardDetails?.cardType || "mastercard",
          cardLogo: getCardLogo(order.cardDetails?.cardType),
        },
        itemInfo: {
          title: order.content?.title || "Frank Martin - Drills and Coaching Philosophies",
          category: order.content?.category || "Basketball Coaching Core",
          image: getThumbnailUrl(order.content?.thumbnailUrl || order.content?.thumbnail, order.content?._id),
        },
      };
    }

    // 🔁 fallback static mock
    return {
      orderId: "#12345678",
      date: "20 May 2025",
      time: "4:50PM",
      items: 1,
      totalAmount: "$20.00",
      customerDetails: {
        name: "John Smith",
        email: "<EMAIL>",
        phone: "Not provided",
      },
      paymentDetails: {
        method: "Mastercard",
        cardNumber: "**** **** **** 1234",
        cardType: "mastercard",
        cardLogo: mastercardLogo,
      },
      itemInfo: {
        title: "Frank Martin",
        category: "Basketball Coaching Core",
        image: getPlaceholderImage(80, 80, "Product"),
      },
    };
  };

  const orderData = getOrderData();
  // Debug log for cardType and cardLogo
  const cardType = orderData.paymentDetails.cardType;
  const cardLogo = getCardLogo(cardType);
  const cardTypeDisplay = getCardTypeDisplayName(cardType);

  useEffect(() => {
    // Scroll to top when component mounts
    window.scrollTo(0, 0);

    // If orderId is passed in URL params, fetch order details
    const urlParams = new URLSearchParams(location.search);
    const orderId = urlParams.get("orderId");

    if (orderId && !order) {
      dispatch(getOrder(orderId));
    }
  }, [dispatch, location.search, order]);

  useEffect(() => {
    // Handle download URL when available
    if (downloadUrl) {
      const link = document.createElement("a");
      link.href = downloadUrl;
      link.download = `order-${orderData.orderId}-content`;
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);
      setIsDownloading(false);
    }
  }, [downloadUrl, orderData.orderId]);

  const generateReceipt = (orderData) => {
    const doc = new jsPDF();

    // Add company logo with 1:1 aspect ratio
    const logoSize = 30; // Square size (width = height)
    const logoY = 10;
    doc.addImage(companyLogo, 'PNG', (doc.internal.pageSize.width - logoSize) / 2, logoY, logoSize, logoSize);

    // Y-offset after logo
    let currentY = logoY + logoSize + 10;

    // Add title
    doc.setFontSize(18);
    doc.text('Order Receipt', doc.internal.pageSize.width / 2, currentY, { align: 'center' });

    // Add date and time
    currentY += 8;
    doc.setFontSize(10);
    doc.text(`Date: ${orderData.date} | Time: ${orderData.time}`, doc.internal.pageSize.width / 2, currentY, { align: 'center' });

    // Helper to add table section
    const addTableSection = (title, data, startY) => {
      doc.setFontSize(12);
      doc.text(title, 14, startY);

      const tableData = Object.entries(data).map(([key, value]) => [key, value]);

      let finalY;
      autoTable(doc, {
        startY: startY + 5,
        head: [],
        body: tableData,
        theme: 'grid',
        styles: { fontSize: 10, cellPadding: 3 },
        columnStyles: {
          0: { fontStyle: 'bold', cellWidth: 60 },
          1: { cellWidth: 100 }
        },
        margin: { left: 14, right: 14 },
        headStyles: { fillColor: [240, 240, 240], textColor: [0, 0, 0] },
        didDrawPage: function (data) {
          finalY = data.cursor.y;
        }
      });

      return finalY;
    };

    // Order Details
    currentY += 10;
    const orderDetails = {
      'Order ID': orderData.orderId,
      'Total Amount': orderData.totalAmount,
      'Items': orderData.items,
      date: formatWithTimezone(order.createdAt || Date.now(), {
        year: 'numeric',
        month: 'long',
        day: 'numeric'
      }),
      time: formatWithTimezone(order.createdAt || Date.now(), {
        hour: '2-digit',
        minute: '2-digit',
        timeZoneName: 'short'
      })
    };
    currentY = addTableSection('Order Details', orderDetails, currentY);

    // Customer Information
    currentY += 10;
    const customerDetails = {
      'Name': orderData.customerDetails.name,
      'Email': orderData.customerDetails.email,
      'Phone': orderData.customerDetails.phone
    };
    currentY = addTableSection('Customer Information', customerDetails, currentY);

    // Payment Information
    currentY += 10;
    const paymentDetails = {
      'Payment Method': orderData.paymentDetails.method,
      'Card Number': orderData.paymentDetails.cardNumber
    };
    currentY = addTableSection('Payment Information', paymentDetails, currentY);

    // Item Details
    currentY += 10;
    const itemDetails = {
      'Title': orderData.itemInfo.title,
      'Category': orderData.itemInfo.category
    };
    currentY = addTableSection('Item Details', itemDetails, currentY);

    // Footer
    currentY += 15;
    doc.setFontSize(10);
    doc.text('Thank you for your purchase!', doc.internal.pageSize.width / 2, currentY, { align: 'center' });
    doc.text('For any questions, <NAME_EMAIL>', doc.internal.pageSize.width / 2, currentY + 5, { align: 'center' });

    return doc;
  };


  const downloadReceipt = (orderData) => {
    const doc = generateReceipt(orderData);
    doc.save(`receipt-${orderData.orderId.replace('#', '')}.pdf`);
  };

  const handleDownload = async () => {
    if (isDownloading) return;
    setIsDownloading(true);
    try {
      // Download receipt only
      downloadReceipt(orderData);
      // Content download functionality has been disabled for security purposes
    } catch (error) {
      console.error("Download failed:", error);
    } finally {
      setIsDownloading(false);
    }
  };

  const handleGoToHomepage = () => navigate("/");

  return (
    <div className="thank-you-page">
      <div className="thank-you-container max-container">
        {/* ✅ Header */}
        <div className="success-header">
          <div className="success-icon">
            <img src={Thankyou} alt="thankyou" />
          </div>
          <h1 className="success-title">Congratulation for your order!</h1>
          <p className="success-message">We will update you for the delivery status soon via Email or SMS.</p>
        </div>

        {/* 📦 Order Info */}
        <div className="order-info-card">
          <h2 className="order-info-title">Order Information</h2>
          <div className="order-details-grid">
            <div className="order-details-grid-container">
              <div className="order-detail-item">
                <span className="detail-label">Order Id:</span>
                <span className="detail-value">{orderData.orderId}</span>
              </div>
              <div className="order-detail-item">
                <span className="detail-label">Items:</span>
                <span className="detail-value">{orderData.items}</span>
              </div>
            </div>
            <div className="vertical-line"></div>
            <div className="order-details-grid-container">
              <div className="order-detail-item">
                <span className="detail-label">Date:</span>
                <span className="detail-value">{orderData.date} | {orderData.time}</span>
              </div>
              <div className="order-detail-item">
                <span className="detail-label">Total Amount:</span>
                <span className="detail-value">{orderData.totalAmount}</span>
              </div>
            </div>
          </div>

          {/* 👤 Customer & 💳 Payment */}
          <div className="details-section">
            <div className="customer-details">
              <h3 className="section-title">Customer Details</h3>
              <div className="detail-group">
                <div className="detail-row"><span className="detail-label">Name:</span><span className="detail-value">{orderData.customerDetails.name}</span></div>
                <div className="detail-row"><span className="detail-label">Email Address:</span><span className="detail-value">{orderData.customerDetails.email}</span></div>
                <div className="detail-row"><span className="detail-label">Phone Number:</span><span className="detail-value">{orderData.customerDetails.phone}</span></div>
              </div>
            </div>
            <div className="vertical-line"></div>
            <div className="payment-details">
              <h3 className="section-title">Payment Details</h3>
              <div className="payment-method">
                <img
                  src={cardLogo}
                  alt={cardTypeDisplay}
                  className="payment-logo"
                />
                <span className="card-number">
                  {orderData.paymentDetails.cardNumber}
                </span>
              </div>
            </div>
          </div>

          {/* 📘 Item Info */}
          <div className="item-info-section">
            <h3 className="section-title">Item Info</h3>
            <div className="item-info-content">
              <div className="item-image">
                <img
                  src={orderData.itemInfo.image}
                  alt={orderData.itemInfo.title}
                  className="product-image"
                  onError={(e) => {
                    console.error("Thumbnail failed to load:", e.target.src);
                    e.target.src = getPlaceholderImage(80, 80, "Product");
                  }}
                />
              </div>
              <div className="item-details">
                <h4 className="item-title">{orderData.itemInfo.title}</h4>
                <p className="item-category">By {orderData.itemInfo.category}</p>
              </div>
            </div>
          </div>
        </div>
        {/* Action Buttons */}
        <div className="action-buttons">
          <button
            className=" download-btn btn-outline"
            onClick={handleDownload}
            disabled={isDownloading}
          >
            {isDownloading ? (
              <FaSpinner className="btn-icon spinning" />
            ) : (
              <FaDownload className="btn-icon" />
            )}
            {isDownloading ? "Downloading..." : "Download"}
          </button>
          <button className="btn homepage-btn" onClick={handleGoToHomepage}>
            <MdHome className="btn-icon" />
            Go to Homepage
          </button>
        </div>
      </div>
    </div>
  );
};

export default ThankYou;
