const ffmpeg = require('fluent-ffmpeg');
const { PDFDocument } = require('pdf-lib');
const fs = require('fs');
const path = require('path');
const { getS3Instance, isUsingS3Storage } = require('./storageHelper');

// Configure ffmpeg path using ffmpeg-static for all environments
// This ensures FFmpeg is available even if not installed system-wide
try {
  const ffmpegStatic = require('ffmpeg-static');
  if (ffmpegStatic) {
    ffmpeg.setFfmpegPath(ffmpegStatic);
    console.log(`[Setup] FFmpeg path set to: ${ffmpegStatic}`);
  } else {
    console.warn('[Setup] ffmpeg-static returned null, will try to use system FFmpeg');
  }
} catch (error) {
  console.warn('[Setup] Failed to load ffmpeg-static, will try to use system FFmpeg:', error.message);
}

// Get S3 instance if credentials are available (cached instance)
const s3 = getS3Instance();

/**
 * Generate video preview (10-second clip from the beginning)
 * @param {string} inputPath - Path to the original video file
 * @param {string} outputFileName - Name for the preview file
 * @param {boolean} isS3Upload - Whether to upload to S3 or save locally
 * @returns {Promise<string>} - URL of the generated preview
 */
const generateVideoPreview = async (inputPath, outputFileName, isS3Upload = false) => {
  return new Promise((resolve, reject) => {
    try {
      const previewFileName = `${path.parse(outputFileName).name}_preview${path.parse(outputFileName).ext}`;

      console.log(`[Preview] Starting video preview generation for: ${outputFileName}`);
      console.log(`[Preview] Input path: ${inputPath}`);
      console.log(`[Preview] Is S3 upload: ${isS3Upload}`);

      if (isS3Upload && s3) {
        // For S3 uploads, we need to download the file first, process it, then upload the preview
        generateVideoPreviewS3(inputPath, previewFileName)
          .then((result) => {
            console.log(`[Preview] S3 video preview generated successfully: ${result}`);
            resolve(result);
          })
          .catch((error) => {
            console.error(`[Preview] S3 video preview generation failed:`, error);
            reject(error);
          });
      } else {
        // Local file processing
        const outputPath = path.join('./uploads/previews/', previewFileName);

        // Ensure preview directory exists
        const previewDir = path.dirname(outputPath);
        if (!fs.existsSync(previewDir)) {
          console.log(`[Preview] Creating preview directory: ${previewDir}`);
          fs.mkdirSync(previewDir, { recursive: true });
        }

        // Validate input file exists
        if (!fs.existsSync(inputPath)) {
          const error = new Error(`Input video file not found: ${inputPath}`);
          console.error(`[Preview] ${error.message}`);
          reject(error);
          return;
        }

        console.log(`[Preview] Processing local video file to: ${outputPath}`);

        // Add timeout for FFmpeg processing
        const ffmpegTimeout = setTimeout(() => {
          console.error(`[Preview] FFmpeg timeout after 3 minutes for: ${outputFileName}`);
          reject(new Error('Video preview generation timeout'));
        }, 180000); // 3 minutes timeout

        ffmpeg(inputPath)
          .setStartTime(0) // Start from beginning
          .setDuration(10) // 10 seconds duration
          .videoCodec('libx264') // Ensure compatibility
          .audioCodec('aac') // Ensure audio compatibility
          .format('mp4') // Ensure MP4 format
          .size('?x480') // Reduce resolution to 480p for smaller file size
          .videoBitrate('500k') // Reduce bitrate for smaller file size
          .output(outputPath)
          .on('start', (commandLine) => {
            console.log(`[Preview] FFmpeg command: ${commandLine}`);
          })
          .on('progress', (progress) => {
            console.log(`[Preview] Processing: ${Math.round(progress.percent || 0)}% done`);
          })
          .on('end', () => {
            clearTimeout(ffmpegTimeout);
            const previewUrl = `/uploads/previews/${previewFileName}`;
            console.log(`[Preview] Video preview generated successfully: ${previewUrl}`);

            // Verify the output file was created and has content
            if (fs.existsSync(outputPath)) {
              const stats = fs.statSync(outputPath);
              console.log(`[Preview] Preview file size: ${stats.size} bytes`);
              if (stats.size > 0) {
                resolve(previewUrl);
              } else {
                reject(new Error('Generated preview file is empty'));
              }
            } else {
              reject(new Error('Preview file was not created'));
            }
          })
          .on('error', (err) => {
            clearTimeout(ffmpegTimeout);
            console.error(`[Preview] FFmpeg error:`, err);
            reject(new Error(`Failed to generate video preview: ${err.message}`));
          })
          .run();
      }
    } catch (error) {
      console.error(`[Preview] Error in generateVideoPreview:`, error);
      reject(error);
    }
  });
};

/**
 * Generate video preview for S3 stored files
 * @param {string} s3Url - S3 URL of the original video
 * @param {string} previewFileName - Name for the preview file
 * @returns {Promise<string>} - S3 URL of the generated preview
 */
const generateVideoPreviewS3 = async (s3Url, previewFileName) => {
  return new Promise((resolve, reject) => {
    try {
      // Create temporary local paths
      const tempDir = './temp';
      if (!fs.existsSync(tempDir)) {
        fs.mkdirSync(tempDir, { recursive: true });
      }

      const tempInputPath = path.join(tempDir, `temp_${Date.now()}_input.mp4`);
      const tempOutputPath = path.join(tempDir, `temp_${Date.now()}_preview.mp4`);

      // Download original file from S3
      const bucketName = process.env.AWS_BUCKET_NAME;

      // Extract and validate the S3 key from the URL
      const key = validateAndExtractS3Key(s3Url, bucketName);

      if (!key) {
        reject(new Error(`Failed to extract valid S3 key from URL: ${s3Url}`));
        return;
      }

      // Get file size first to determine download strategy
      getS3FileSize(bucketName, key)
        .then((fileSize) => {
          const strategy = getPartialDownloadStrategy(fileSize);

          console.log(`[Preview] Download strategy for ${key}:`, {
            fileSize: `${(fileSize / (1024 * 1024)).toFixed(2)}MB`,
            strategy: strategy.strategy,
            downloadSize: `${(strategy.downloadSize / (1024 * 1024)).toFixed(2)}MB`,
            shouldUsePartial: strategy.shouldUsePartial
          });

          // Use appropriate download method based on strategy
          if (strategy.shouldUsePartial) {
            return downloadPartialFromS3(bucketName, key, tempInputPath, strategy.downloadSize);
          } else {
            // Full download for small files
            return downloadFullFromS3(bucketName, key, tempInputPath);
          }
        })
        .then(() => {
          console.log(`[Preview] Download completed for: ${key}`);
          processDownloadedVideo();
        })
        .catch((downloadError) => {
          console.error(`[Preview] Download failed for ${key}:`, downloadError);
          // Clean up temporary files
          try {
            if (fs.existsSync(tempInputPath)) fs.unlinkSync(tempInputPath);
            if (fs.existsSync(tempOutputPath)) fs.unlinkSync(tempOutputPath);
          } catch (cleanupError) {
            console.error('[Preview] Error cleaning up temp files:', cleanupError);
          }
          reject(new Error(`Failed to download video for preview: ${downloadError.message}`));
        });

      // Function to process the downloaded video file
      const processDownloadedVideo = () => {
        // Add timeout for S3 FFmpeg processing
        const s3FfmpegTimeout = setTimeout(() => {
          console.error(`[Preview] S3 FFmpeg timeout after 3 minutes for: ${previewFileName}`);
          // Clean up temporary files
          try {
            if (fs.existsSync(tempInputPath)) fs.unlinkSync(tempInputPath);
            if (fs.existsSync(tempOutputPath)) fs.unlinkSync(tempOutputPath);
          } catch (cleanupError) {
            console.error('[Preview] Error cleaning up temp files:', cleanupError);
          }
          reject(new Error('S3 video preview generation timeout'));
        }, 180000); // 3 minutes timeout

        // Process the downloaded file
        ffmpeg(tempInputPath)
          .setStartTime(0)
          .setDuration(10)
          .videoCodec('libx264')
          .audioCodec('aac')
          .format('mp4')
          .size('?x480')
          .videoBitrate('500k')
          .output(tempOutputPath)
          .on('start', (commandLine) => {
            console.log(`[Preview] S3 FFmpeg command: ${commandLine}`);
          })
          .on('progress', (progress) => {
            console.log(`[Preview] S3 Processing: ${Math.round(progress.percent || 0)}% done`);
          })
          .on('end', () => {
            clearTimeout(s3FfmpegTimeout);
            // Upload preview to S3
            const uploadParams = {
              Bucket: bucketName,
              Key: `previews/${previewFileName}`,
              Body: fs.createReadStream(tempOutputPath),
              ContentType: 'video/mp4'
              // Removed ACL setting - bucket policy handles public access
            };

            s3.upload(uploadParams, (err, data) => {
              // Clean up temporary files
              try {
                if (fs.existsSync(tempInputPath)) fs.unlinkSync(tempInputPath);
                if (fs.existsSync(tempOutputPath)) fs.unlinkSync(tempOutputPath);
              } catch (cleanupError) {
                console.error('[Preview] Error cleaning up temp files:', cleanupError);
              }

              if (err) {
                console.error('Error uploading preview to S3:', err);
                reject(new Error(`Failed to upload preview to S3: ${err.message}`));
              } else {
                console.log(`[Preview] S3 preview uploaded successfully: ${data.Location}`);
                // Use clean URL construction instead of data.Location to avoid encoding issues
                const cleanPreviewUrl = `https://${bucketName}.s3.${process.env.AWS_REGION}.amazonaws.com/previews/${previewFileName}`;
                console.log(`[Preview] Returning clean preview URL: ${cleanPreviewUrl}`);
                resolve(cleanPreviewUrl);
              }
            });
          })
          .on('error', (err) => {
            clearTimeout(s3FfmpegTimeout);
            console.error(`[Preview] S3 FFmpeg error:`, err);
            // Clean up temporary files
            try {
              if (fs.existsSync(tempInputPath)) fs.unlinkSync(tempInputPath);
              if (fs.existsSync(tempOutputPath)) fs.unlinkSync(tempOutputPath);
            } catch (cleanupError) {
              console.error('[Preview] Error cleaning up temp files:', cleanupError);
            }
            reject(new Error(`Failed to generate S3 video preview: ${err.message}`));
          })
          .run();
      };

    } catch (error) {
      console.error('Error in generateVideoPreviewS3:', error);
      reject(error);
    }
  });
};

/**
 * Download full file from S3 (fallback for small files)
 * @param {string} bucketName - S3 bucket name
 * @param {string} key - S3 object key
 * @param {string} tempPath - Local temporary file path
 * @returns {Promise<void>}
 */
const downloadFullFromS3 = async (bucketName, key, tempPath, retries = 2) => {
  for (let attempt = 1; attempt <= retries; attempt++) {
    try {
      console.log(`[Preview] Starting full download from S3 key: ${key} (attempt ${attempt}/${retries})`);

      const downloadParams = {
        Bucket: bucketName,
        Key: key
      };

      await new Promise((resolve, reject) => {
        const fileStream = fs.createWriteStream(tempPath);
        let downloadedBytes = 0;

        const s3Stream = s3.getObject(downloadParams).createReadStream();

        s3Stream.on('error', (s3Error) => {
          console.error(`[Preview] S3 full download error for key: ${key} (attempt ${attempt}/${retries})`, s3Error.code || s3Error.message);
          fileStream.destroy();
          reject(s3Error);
        });

        s3Stream.on('data', (chunk) => {
          downloadedBytes += chunk.length;
        });

        s3Stream.pipe(fileStream);

        fileStream.on('close', () => {
          console.log(`[Preview] Full download completed: ${downloadedBytes} bytes`);
          resolve();
        });

        fileStream.on('error', (fileError) => {
          console.error(`[Preview] File write error during full download:`, fileError);
          reject(fileError);
        });
      });

      // If we get here, download was successful
      return;

    } catch (error) {
      console.error(`[Preview] Full download attempt ${attempt}/${retries} failed:`, error.code || error.message);

      if (attempt === retries) {
        // Last attempt failed
        if (error.code === 'NoSuchKey' || error.code === 'NotFound') {
          throw new Error(`File not found in S3: ${key}. The file may not have been uploaded yet or the key is incorrect.`);
        }
        throw new Error(`Full download failed after ${retries} attempts: ${error.message}`);
      }

      // Wait before retry
      const waitTime = 2000 * attempt; // 2s, 4s
      console.log(`[Preview] Waiting ${waitTime}ms before retry...`);
      await new Promise(resolve => setTimeout(resolve, waitTime));
    }
  }
};

/**
 * Generate PDF preview (first page only)
 * @param {string} inputPath - Path to the original PDF file
 * @param {string} outputFileName - Name for the preview file
 * @param {boolean} isS3Upload - Whether to upload to S3 or save locally
 * @returns {Promise<string>} - URL of the generated preview
 */
const generatePdfPreview = async (inputPath, outputFileName, isS3Upload = false) => {
  try {
    const previewFileName = `${path.parse(outputFileName).name}_preview${path.parse(outputFileName).ext}`;

    console.log(`[Preview] Starting PDF preview generation for: ${outputFileName}`);
    console.log(`[Preview] Input path: ${inputPath}`);
    console.log(`[Preview] Is S3 upload: ${isS3Upload}`);

    if (isS3Upload && s3) {
      const result = await generatePdfPreviewS3(inputPath, previewFileName);
      console.log(`[Preview] S3 PDF preview generated successfully: ${result}`);
      return result;
    } else {
      // Local file processing
      const outputPath = path.join('./uploads/previews/', previewFileName);

      // Ensure preview directory exists
      const previewDir = path.dirname(outputPath);
      if (!fs.existsSync(previewDir)) {
        console.log(`[Preview] Creating preview directory: ${previewDir}`);
        fs.mkdirSync(previewDir, { recursive: true });
      }

      // Validate input file exists
      if (!fs.existsSync(inputPath)) {
        const error = new Error(`Input PDF file not found: ${inputPath}`);
        console.error(`[Preview] ${error.message}`);
        throw error;
      }

      console.log(`[Preview] Processing local PDF file to: ${outputPath}`);

      // Read the original PDF
      const existingPdfBytes = fs.readFileSync(inputPath);
      console.log(`[Preview] Original PDF size: ${existingPdfBytes.length} bytes`);

      const pdfDoc = await PDFDocument.load(existingPdfBytes);
      const pageCount = pdfDoc.getPageCount();
      console.log(`[Preview] PDF has ${pageCount} pages, extracting first page`);

      if (pageCount === 0) {
        throw new Error('PDF file has no pages');
      }

      // Create a new PDF with only the first page
      const newPdfDoc = await PDFDocument.create();
      const [firstPage] = await newPdfDoc.copyPages(pdfDoc, [0]);
      newPdfDoc.addPage(firstPage);

      // Save the preview PDF
      const pdfBytes = await newPdfDoc.save();
      fs.writeFileSync(outputPath, pdfBytes);

      console.log(`[Preview] Preview PDF size: ${pdfBytes.length} bytes`);

      // Verify the output file was created and has content
      if (fs.existsSync(outputPath)) {
        const stats = fs.statSync(outputPath);
        console.log(`[Preview] Preview file created successfully with size: ${stats.size} bytes`);
        if (stats.size === 0) {
          throw new Error('Generated preview file is empty');
        }
      } else {
        throw new Error('Preview file was not created');
      }

      const previewUrl = `/uploads/previews/${previewFileName}`;
      console.log(`[Preview] PDF preview generated successfully: ${previewUrl}`);
      return previewUrl;
    }
  } catch (error) {
    console.error(`[Preview] Error generating PDF preview:`, error);
    throw new Error(`Failed to generate PDF preview: ${error.message}`);
  }
};

/**
 * Generate PDF preview for S3 stored files
 * @param {string} s3Url - S3 URL of the original PDF
 * @param {string} previewFileName - Name for the preview file
 * @returns {Promise<string>} - S3 URL of the generated preview
 */
const generatePdfPreviewS3 = async (s3Url, previewFileName) => {
  try {
    // Download original PDF from S3
    const bucketName = process.env.AWS_BUCKET_NAME;

    // Extract and validate the S3 key from the URL
    const key = validateAndExtractS3Key(s3Url, bucketName);

    if (!key) {
      throw new Error(`Failed to extract valid S3 key from PDF URL: ${s3Url}`);
    }

    console.log(`[Preview] Starting PDF preview generation from S3`);
    console.log(`[Preview] Bucket: ${bucketName}, Key: ${key}`);
    console.log(`[Preview] Original PDF S3 URL: ${s3Url}`);

    // Get file size first to determine download strategy
    console.log(`[Preview] Getting PDF file size from S3...`);
    let fileSize;
    try {
      fileSize = await getS3FileSize(bucketName, key);
      const fileSizeMB = fileSize / (1024 * 1024);
      console.log(`[Preview] PDF file size: ${fileSizeMB.toFixed(2)}MB`);

      // Validate file size against limits
      const maxSizeMB = 50; // PDF limit from config
      if (fileSizeMB > maxSizeMB) {
        throw new Error(`PDF file size (${fileSizeMB.toFixed(2)}MB) exceeds maximum limit of ${maxSizeMB}MB`);
      }

      // Estimate download time (assuming 1MB/s average speed)
      const estimatedDownloadTimeSeconds = Math.ceil(fileSizeMB);
      console.log(`[Preview] Estimated download time: ~${estimatedDownloadTimeSeconds} seconds`);

    } catch (sizeError) {
      console.error(`[Preview] Failed to get PDF file size:`, sizeError);
      // Continue without size info, but log warning
      console.warn(`[Preview] Proceeding without file size information - this may affect download strategy`);
      fileSize = 0;
    }

    const downloadParams = {
      Bucket: bucketName,
      Key: key
    };

    // Add timeout and progress tracking for PDF download
    let data;
    try {
      console.log(`[Preview] Starting PDF download from S3...`);
      const downloadStartTime = Date.now();

      // Use appropriate download strategy based on PDF file size
      const fileSizeMB = fileSize / (1024 * 1024);

      if (fileSize === 0) {
        // Unknown file size - use conservative approach with larger partial download
        console.log(`[Preview] Unknown PDF file size, using partial download (10MB) as fallback`);
        try {
          data = await downloadPartialPdfForPreview(bucketName, key, 10 * 1024 * 1024); // 10MB partial download
        } catch (partialError) {
          console.warn(`[Preview] Partial download failed for unknown size PDF, attempting full download:`, partialError.message);
          data = await downloadPdfWithRetry(bucketName, key, 600000, 2); // 10 minutes timeout, 2 retries
        }
      } else if (fileSizeMB > 15) {
        // For PDFs > 15MB, try progressive download strategy
        console.log(`[Preview] Large PDF detected (${fileSizeMB.toFixed(2)}MB), using progressive download strategy`);

        // Try with larger partial download first (10MB for better PDF structure coverage)
        try {
          console.log(`[Preview] Attempting 10MB partial download for better PDF structure coverage`);
          data = await downloadPartialPdfForPreview(bucketName, key, 10 * 1024 * 1024); // 10MB partial download
        } catch (partialError) {
          console.warn(`[Preview] 10MB partial download failed, falling back to full download:`, partialError.message);
          // Fallback to full download if partial fails
          data = await downloadPdfWithRetry(bucketName, key, 600000, 2); // 10 minutes timeout, 2 retries
        }
      } else if (fileSizeMB > 10) {
        // For PDFs 10-15MB, use direct download with longer timeout and retry
        console.log(`[Preview] Medium PDF (${fileSizeMB.toFixed(2)}MB), using direct download with extended timeout`);
        data = await downloadPdfWithRetry(bucketName, key, 300000, 2); // 5 minutes timeout, 2 retries
      } else {
        // For PDFs < 10MB, use direct download with standard timeout and retry
        console.log(`[Preview] Small PDF (${fileSizeMB.toFixed(2)}MB), using direct download`);
        data = await downloadPdfWithRetry(bucketName, key, 240000, 2); // 4 minutes timeout, 2 retries
      }

      const downloadTime = Date.now() - downloadStartTime;
      const downloadedMB = data.Body.length / (1024 * 1024);
      console.log(`[Preview] PDF download completed in ${downloadTime}ms (${downloadedMB.toFixed(2)}MB)`);
    } catch (s3Error) {
      console.error(`[Preview] S3 PDF download failed for key: ${key}`, s3Error);
      if (s3Error.code === 'NoSuchKey') {
        throw new Error(`PDF file not found in S3: ${key}. The file may have been moved or deleted.`);
      }
      if (s3Error.message.includes('timeout')) {
        throw new Error(`PDF download timeout: File may be too large or connection is slow`);
      }
      throw new Error(`S3 PDF download failed: ${s3Error.message}`);
    }

    const existingPdfBytes = data.Body;

    // Process PDF with progress tracking and fallback handling
    console.log(`[Preview] Starting PDF processing...`);
    const processingStartTime = Date.now();

    let pdfBytes;
    try {
      console.log(`[Preview] Loading PDF document...`);
      const pdfDoc = await PDFDocument.load(existingPdfBytes);
      console.log(`[Preview] PDF loaded successfully, pages: ${pdfDoc.getPageCount()}`);

      console.log(`[Preview] Creating preview with first page...`);
      const newPdfDoc = await PDFDocument.create();
      const [firstPage] = await newPdfDoc.copyPages(pdfDoc, [0]);
      newPdfDoc.addPage(firstPage);

      console.log(`[Preview] Saving preview PDF...`);
      pdfBytes = await newPdfDoc.save();

      const processingTime = Date.now() - processingStartTime;
      const previewSizeMB = pdfBytes.length / (1024 * 1024);
      console.log(`[Preview] PDF processing completed in ${processingTime}ms (preview: ${previewSizeMB.toFixed(2)}MB)`);
    } catch (pdfError) {
      console.error(`[Preview] PDF processing failed:`, pdfError.message);

      // Check if this is a partial download parsing error
      if (pdfError.message.includes('Failed to parse') && existingPdfBytes.length < fileSize) {
        console.log(`[Preview] PDF parsing failed with partial download (${(existingPdfBytes.length / (1024 * 1024)).toFixed(2)}MB of ${(fileSize / (1024 * 1024)).toFixed(2)}MB)`);
        console.log(`[Preview] Attempting full PDF download for complete parsing...`);

        try {
          // Retry with full download
          const fullData = await downloadPdfWithRetry(bucketName, key, 600000, 2); // 10 minutes timeout
          const fullPdfBytes = fullData.Body;

          console.log(`[Preview] Full PDF downloaded (${(fullPdfBytes.length / (1024 * 1024)).toFixed(2)}MB), retrying processing...`);

          const pdfDoc = await PDFDocument.load(fullPdfBytes);
          console.log(`[Preview] Full PDF loaded successfully, pages: ${pdfDoc.getPageCount()}`);

          const newPdfDoc = await PDFDocument.create();
          const [firstPage] = await newPdfDoc.copyPages(pdfDoc, [0]);
          newPdfDoc.addPage(firstPage);

          pdfBytes = await newPdfDoc.save();

          const processingTime = Date.now() - processingStartTime;
          const previewSizeMB = pdfBytes.length / (1024 * 1024);
          console.log(`[Preview] PDF processing completed with full download in ${processingTime}ms (preview: ${previewSizeMB.toFixed(2)}MB)`);
        } catch (fullDownloadError) {
          console.error(`[Preview] Full download fallback also failed:`, fullDownloadError.message);
          throw new Error(`PDF processing failed even with full download: ${fullDownloadError.message}`);
        }
      } else {
        throw new Error(`PDF processing failed: ${pdfError.message}`);
      }
    }

    // Upload preview to S3 with progress tracking
    console.log(`[Preview] Uploading PDF preview to S3...`);
    const uploadStartTime = Date.now();

    const uploadParams = {
      Bucket: bucketName,
      Key: `previews/${previewFileName}`,
      Body: pdfBytes,
      ContentType: 'application/pdf'
      // Removed ACL setting - bucket policy handles public access
    };

    try {
      await s3.upload(uploadParams).promise();
      const uploadTime = Date.now() - uploadStartTime;
      console.log(`[Preview] PDF preview upload completed in ${uploadTime}ms`);

      // Use clean URL construction instead of uploadResult.Location to avoid encoding issues
      const cleanPreviewUrl = `https://${bucketName}.s3.${process.env.AWS_REGION}.amazonaws.com/previews/${previewFileName}`;
      console.log(`[Preview] PDF preview uploaded successfully, returning clean URL: ${cleanPreviewUrl}`);
      return cleanPreviewUrl;
    } catch (uploadError) {
      console.error(`[Preview] PDF preview upload failed:`, uploadError);
      throw new Error(`PDF preview upload failed: ${uploadError.message}`);
    }

  } catch (error) {
    console.error('Error generating PDF preview for S3:', error);
    throw new Error(`Failed to generate PDF preview for S3: ${error.message}`);
  }
};

/**
 * Generate preview based on content type
 * @param {string} contentType - Type of content (Video, PDF, etc.)
 * @param {string} filePath - Path to the original file
 * @param {string} fileName - Original file name
 * @param {boolean} isS3Upload - Whether file is stored on S3 (optional, auto-detected if not provided)
 * @returns {Promise<string|null>} - URL of the generated preview or null if not supported
 */
const generatePreview = async (contentType, filePath, fileName, isS3Upload = null) => {
  // Auto-detect storage type if not explicitly provided
  if (isS3Upload === null) {
    isS3Upload = isUsingS3Storage();
  }
  try {
    console.log(`[Preview] Starting preview generation for ${contentType} file: ${fileName}`);

    // Get file extension once for all cases
    const fileExt = path.extname(fileName).toLowerCase();

    switch (contentType.toLowerCase()) {
      case 'video':
        // Check if it's a video file by extension
        const videoExtensions = ['.mp4', '.avi', '.mov', '.wmv', '.flv', '.webm', '.mkv'];

        if (videoExtensions.includes(fileExt)) {
          console.log(`[Preview] Generating video preview for: ${fileName}`);
          return await generateVideoPreview(filePath, fileName, isS3Upload);
        } else {
          console.log(`[Preview] Video file extension ${fileExt} not supported for preview generation`);
          return null;
        }

      case 'pdf':
      case 'document':
        if (fileExt === '.pdf') {
          console.log(`[Preview] Generating PDF preview for: ${fileName}`);
          return await generatePdfPreview(filePath, fileName, isS3Upload);
        } else {
          console.log(`[Preview] Non-PDF document detected: ${fileName} (${fileExt})`);
          console.log(`[Preview] Document preview will use original file for display`);

          // For non-Office documents, return the original file URL as preview
          if (isS3Upload) {
            // For S3 files, return the S3 URL directly
            console.log(`[Preview] Returning S3 URL for document preview: ${filePath}`);
            return filePath;
          } else {
            // For local files, ensure proper URL format
            // If filePath is already a proper URL path (starts with /uploads), use it
            if (filePath.startsWith('/uploads/')) {
              console.log(`[Preview] Returning existing URL path for document preview: ${filePath}`);
              return filePath;
            } else {
              // If filePath is a local file path, convert to URL format
              const normalizedPath = filePath.replace(/\\/g, '/'); // Convert backslashes to forward slashes
              let urlPath;

              if (normalizedPath.startsWith('./uploads/')) {
                urlPath = normalizedPath.substring(1); // Remove leading dot
              } else if (normalizedPath.startsWith('uploads/')) {
                urlPath = '/' + normalizedPath; // Add leading slash
              } else {
                // Assume it's just the filename and construct the full path
                urlPath = `/uploads/${fileName}`;
              }

              console.log(`[Preview] Converted local path to URL for document preview: ${urlPath}`);
              return urlPath;
            }
          }
        }

      default:
        console.log(`[Preview] Preview generation not supported for content type: ${contentType}`);
        return null;
    }
  } catch (error) {
    console.error(`[Preview] Error in generatePreview for ${fileName}:`, error);
    console.error(`[Preview] Error details:`, {
      contentType,
      fileName,
      filePath,
      isS3Upload,
      errorMessage: error.message,
      errorStack: error.stack
    });
    // Don't throw error - just log it and return null so content creation doesn't fail
    return null;
  }
};



/**
 * Validate file type for preview generation
 * @param {string} contentType - Content type
 * @param {string} fileName - File name
 * @returns {boolean} - Whether preview can be generated
 */
const canGeneratePreview = (contentType, fileName) => {
  const fileExt = path.extname(fileName).toLowerCase();

  switch (contentType.toLowerCase()) {
    case 'video':
      const videoExtensions = ['.mp4', '.avi', '.mov', '.wmv', '.flv', '.webm', '.mkv'];
      return videoExtensions.includes(fileExt);

    case 'pdf':
    case 'document':
      // Support document types - only PDF supported
      const documentExtensions = ['.pdf'];
      return documentExtensions.includes(fileExt);

    default:
      return false;
  }
};

/**
 * Clean up preview files when content is deleted
 * @param {string} previewUrl - URL of the preview file to delete
 * @param {boolean} isS3Upload - Whether the file is stored on S3 (optional, auto-detected if not provided)
 * @returns {Promise<boolean>} - Whether cleanup was successful
 */
const cleanupPreviewFile = async (previewUrl, isS3Upload = null) => {
  // Auto-detect storage type if not explicitly provided
  if (isS3Upload === null) {
    const { isS3Url } = require('./storageHelper');
    isS3Upload = isS3Url(previewUrl);
  }
  try {
    if (!previewUrl) {
      console.log('[Cleanup] No preview URL provided, skipping cleanup');
      return true;
    }

    console.log(`[Cleanup] Starting preview file cleanup for: ${previewUrl}`);

    if (isS3Upload && s3) {
      // S3 cleanup
      const bucketName = process.env.AWS_BUCKET_NAME;

      // Extract the full S3 key from the preview URL
      let key;
      if (previewUrl.includes('amazonaws.com')) {
        // For standard S3 URLs, extract everything after the bucket name
        const urlParts = previewUrl.split('/');
        const bucketIndex = urlParts.findIndex(part => part.includes(bucketName));
        if (bucketIndex !== -1) {
          key = urlParts.slice(bucketIndex + 1).join('/');
        } else {
          // Fallback: assume everything after the domain is the key
          const domainIndex = urlParts.findIndex(part => part.includes('amazonaws.com'));
          key = urlParts.slice(domainIndex + 1).join('/');
        }
      } else {
        // For relative URLs like /uploads/previews/filename.ext
        const fileName = previewUrl.split('/').pop();
        key = `previews/${fileName}`;
      }

      const deleteParams = {
        Bucket: bucketName,
        Key: key
      };

      try {
        await s3.deleteObject(deleteParams).promise();
        console.log(`[Cleanup] S3 preview file deleted successfully: ${key}`);
        return true;
      } catch (s3Error) {
        console.error(`[Cleanup] S3 delete failed for key: ${key}`, s3Error);
        if (s3Error.code === 'NoSuchKey') {
          console.log(`[Cleanup] File already deleted or doesn't exist: ${key}`);
          return true; // Consider this a success since the file is gone
        }
        throw s3Error;
      }
    } else {
      // Local file cleanup
      const fileName = previewUrl.split('/').pop();
      const filePath = path.join('./uploads/previews/', fileName);

      if (fs.existsSync(filePath)) {
        fs.unlinkSync(filePath);
        console.log(`[Cleanup] Local preview file deleted successfully: ${filePath}`);
        return true;
      } else {
        console.log(`[Cleanup] Preview file not found, may have been already deleted: ${filePath}`);
        return true;
      }
    }
  } catch (error) {
    console.error(`[Cleanup] Error cleaning up preview file:`, error);
    // Don't throw error - cleanup failure shouldn't prevent content deletion
    return false;
  }
};

/**
 * Get file size from S3 without downloading the file
 * @param {string} bucketName - S3 bucket name
 * @param {string} key - S3 object key
 * @param {number} retries - Number of retry attempts (default: 3)
 * @returns {Promise<number>} - File size in bytes
 */
const getS3FileSize = async (bucketName, key, retries = 3) => {
  for (let attempt = 1; attempt <= retries; attempt++) {
    try {
      const headParams = {
        Bucket: bucketName,
        Key: key
      };

      console.log(`[Preview] Getting file size for S3 key: ${key} (attempt ${attempt}/${retries})`);
      const headResult = await s3.headObject(headParams).promise();
      console.log(`[Preview] File size retrieved: ${headResult.ContentLength} bytes`);
      return headResult.ContentLength;
    } catch (error) {
      console.error(`[Preview] Error getting S3 file size for ${key} (attempt ${attempt}/${retries}):`, error.code || error.message);

      if (attempt === retries) {
        // Last attempt failed
        if (error.code === 'NotFound') {
          throw new Error(`File not found in S3: ${key}. The file may not have been uploaded yet or the key is incorrect.`);
        }
        throw new Error(`Failed to get file size from S3 after ${retries} attempts: ${error.message}`);
      }

      // Wait before retry (exponential backoff)
      const waitTime = Math.pow(2, attempt - 1) * 1000; // 1s, 2s, 4s
      console.log(`[Preview] Waiting ${waitTime}ms before retry...`);
      await new Promise(resolve => setTimeout(resolve, waitTime));
    }
  }
};

/**
 * Determine partial download size based on file size
 * @param {number} fileSize - Total file size in bytes
 * @returns {Object} - Download strategy with size and shouldUsePartial flag
 */
const getPartialDownloadStrategy = (fileSize) => {
  const fileSizeMB = fileSize / (1024 * 1024);

  console.log(`[Preview] File size: ${fileSizeMB.toFixed(2)}MB`);

  if (fileSizeMB < 10) {
    // Small files: download full file
    return {
      shouldUsePartial: false,
      downloadSize: fileSize,
      strategy: 'full_download'
    };
  } else if (fileSizeMB <= 50) {
    // Medium files: download first 5MB
    const downloadSize = Math.min(5 * 1024 * 1024, fileSize);
    return {
      shouldUsePartial: true,
      downloadSize: downloadSize,
      strategy: 'partial_5mb'
    };
  } else {
    // Large files: download first 10MB
    const downloadSize = Math.min(10 * 1024 * 1024, fileSize);
    return {
      shouldUsePartial: true,
      downloadSize: downloadSize,
      strategy: 'partial_10mb'
    };
  }
};

/**
 * Download partial file from S3 using range requests
 * @param {string} bucketName - S3 bucket name
 * @param {string} key - S3 object key
 * @param {string} tempPath - Local temporary file path
 * @param {number} downloadSize - Number of bytes to download
 * @param {number} retries - Number of retry attempts (default: 2)
 * @returns {Promise<void>}
 */
const downloadPartialFromS3 = async (bucketName, key, tempPath, downloadSize, retries = 2) => {
  for (let attempt = 1; attempt <= retries; attempt++) {
    try {
      console.log(`[Preview] Starting partial download: ${downloadSize} bytes from S3 key: ${key} (attempt ${attempt}/${retries})`);

      const downloadParams = {
        Bucket: bucketName,
        Key: key,
        Range: `bytes=0-${downloadSize - 1}` // Range is inclusive, so subtract 1
      };

      await new Promise((resolve, reject) => {
        const fileStream = fs.createWriteStream(tempPath);
        let downloadedBytes = 0;

        const s3Stream = s3.getObject(downloadParams).createReadStream();

        s3Stream.on('error', (s3Error) => {
          console.error(`[Preview] S3 partial download error for key: ${key} (attempt ${attempt}/${retries})`, s3Error.code || s3Error.message);
          fileStream.destroy();
          reject(s3Error);
        });

        s3Stream.on('data', (chunk) => {
          downloadedBytes += chunk.length;
        });

        s3Stream.pipe(fileStream);

        fileStream.on('close', () => {
          console.log(`[Preview] Partial download completed: ${downloadedBytes} bytes`);
          resolve();
        });

        fileStream.on('error', (fileError) => {
          console.error(`[Preview] File write error during partial download:`, fileError);
          reject(fileError);
        });
      });

      // If we get here, download was successful
      return;

    } catch (error) {
      console.error(`[Preview] Partial download attempt ${attempt}/${retries} failed:`, error.code || error.message);

      if (attempt === retries) {
        // Last attempt failed
        if (error.code === 'NotFound') {
          throw new Error(`File not found in S3: ${key}. The file may not have been uploaded yet or the key is incorrect.`);
        }
        throw new Error(`Partial download failed after ${retries} attempts: ${error.message}`);
      }

      // Wait before retry
      const waitTime = 2000 * attempt; // 2s, 4s
      console.log(`[Preview] Waiting ${waitTime}ms before retry...`);
      await new Promise(resolve => setTimeout(resolve, waitTime));
    }
  }
};

/**
 * Validate and extract S3 key from URL
 * @param {string} s3Url - S3 URL to validate
 * @param {string} bucketName - Expected bucket name
 * @returns {string|null} - Extracted S3 key or null if invalid
 */
const validateAndExtractS3Key = (s3Url, bucketName) => {
  try {
    if (!s3Url || !bucketName) {
      console.error('[S3] Missing URL or bucket name');
      return null;
    }

    console.log(`[S3] Validating URL: ${s3Url}`);
    console.log(`[S3] Expected bucket: ${bucketName}`);

    // Check if it's a valid S3 URL
    if (!s3Url.includes('amazonaws.com')) {
      console.error('[S3] URL does not contain amazonaws.com');
      return null;
    }

    let key;
    const urlParts = s3Url.split('/');

    // Method 1: Find bucket name in URL parts
    const bucketIndex = urlParts.findIndex(part => part.includes(bucketName));
    if (bucketIndex !== -1) {
      key = urlParts.slice(bucketIndex + 1).join('/');
      console.log(`[S3] Key extracted using bucket name method: ${key}`);
    } else {
      // Method 2: Find amazonaws.com and extract everything after
      const domainIndex = urlParts.findIndex(part => part.includes('amazonaws.com'));
      if (domainIndex !== -1) {
        key = urlParts.slice(domainIndex + 1).join('/');
        console.log(`[S3] Key extracted using domain method: ${key}`);
      } else {
        // Method 3: Fallback - assume last 3 parts are the key
        key = urlParts.slice(-3).join('/');
        console.log(`[S3] Key extracted using fallback method: ${key}`);
      }
    }

    // Validate the extracted key
    if (!key || key.length === 0) {
      console.error('[S3] Extracted key is empty');
      return null;
    }

    // Remove any leading slashes
    key = key.replace(/^\/+/, '');

    // Decode URL-encoded characters (e.g., %2F -> /)
    try {
      key = decodeURIComponent(key);
      console.log(`[S3] Key after URL decoding: ${key}`);
    } catch (decodeError) {
      console.warn(`[S3] Failed to decode key, using as-is: ${decodeError.message}`);
    }

    console.log(`[S3] Final validated key: ${key}`);
    return key;

  } catch (error) {
    console.error('[S3] Error validating S3 URL:', error);
    return null;
  }
};

/**
 * Download partial PDF for preview generation (first N bytes only)
 * @param {string} bucketName - S3 bucket name
 * @param {string} key - S3 object key
 * @param {number} downloadSize - Number of bytes to download
 * @param {number} retries - Number of retry attempts (default: 2)
 * @returns {Promise<Object>} - Downloaded data object
 */
const downloadPartialPdfForPreview = async (bucketName, key, downloadSize, retries = 2) => {
  for (let attempt = 1; attempt <= retries; attempt++) {
    try {
      const downloadSizeMB = downloadSize / (1024 * 1024);
      console.log(`[Preview] Starting partial PDF download: ${downloadSizeMB.toFixed(2)}MB (attempt ${attempt}/${retries})`);

      const downloadParams = {
        Bucket: bucketName,
        Key: key,
        Range: `bytes=0-${downloadSize - 1}` // Range is inclusive, so subtract 1
      };

      const downloadPromise = s3.getObject(downloadParams).promise();
      const timeoutPromise = new Promise((_, reject) => {
        setTimeout(() => reject(new Error(`Partial PDF download timeout after 2 minutes`)), 120000); // 2 minutes for partial download
      });

      const data = await Promise.race([downloadPromise, timeoutPromise]);

      const actualDownloadedMB = data.Body.length / (1024 * 1024);
      console.log(`[Preview] Partial PDF download successful: ${actualDownloadedMB.toFixed(2)}MB downloaded`);

      // Log some info about the downloaded content for debugging
      const downloadedBytes = data.Body.length;
      console.log(`[Preview] Downloaded ${downloadedBytes} bytes for PDF parsing`);

      return data;

    } catch (error) {
      console.error(`[Preview] Partial PDF download attempt ${attempt}/${retries} failed:`, error.message);

      if (attempt === retries) {
        // Last attempt failed
        if (error.code === 'NoSuchKey' || error.code === 'NotFound') {
          throw new Error(`PDF file not found in S3: ${key}. The file may not have been uploaded yet.`);
        }
        if (error.code === 'InvalidRange') {
          throw new Error(`PDF file is smaller than requested download size. File may be corrupted or incomplete.`);
        }
        if (error.message.includes('timeout')) {
          throw new Error(`Partial PDF download timeout: Download took longer than expected. Consider using full download.`);
        }
        throw new Error(`Partial PDF download failed after ${retries} attempts: ${error.message}`);
      }

      // Wait before retry
      const waitTime = 2000 * attempt; // 2s, 4s
      console.log(`[Preview] Waiting ${waitTime}ms before retry...`);
      await new Promise(resolve => setTimeout(resolve, waitTime));
    }
  }
};

/**
 * Download PDF with retry logic for direct downloads
 * @param {string} bucketName - S3 bucket name
 * @param {string} key - S3 object key
 * @param {number} timeoutMs - Timeout in milliseconds
 * @param {number} retries - Number of retry attempts
 * @returns {Promise<Object>} - Downloaded data object
 */
const downloadPdfWithRetry = async (bucketName, key, timeoutMs, retries = 2) => {
  for (let attempt = 1; attempt <= retries; attempt++) {
    try {
      console.log(`[Preview] PDF direct download attempt ${attempt}/${retries} (timeout: ${timeoutMs/1000}s)`);

      const downloadParams = {
        Bucket: bucketName,
        Key: key
      };

      const downloadPromise = s3.getObject(downloadParams).promise();
      const timeoutPromise = new Promise((_, reject) => {
        setTimeout(() => reject(new Error(`PDF download timeout after ${timeoutMs/1000} seconds`)), timeoutMs);
      });

      const data = await Promise.race([downloadPromise, timeoutPromise]);
      console.log(`[Preview] PDF direct download successful on attempt ${attempt}`);
      return data;

    } catch (error) {
      console.error(`[Preview] PDF direct download attempt ${attempt}/${retries} failed:`, error.message);

      if (attempt === retries) {
        // Last attempt failed
        if (error.code === 'NoSuchKey' || error.code === 'NotFound') {
          throw new Error(`PDF file not found in S3: ${key}. The file may not have been uploaded yet.`);
        }
        if (error.message.includes('timeout')) {
          throw new Error(`PDF download timeout: File download took longer than ${timeoutMs/1000} seconds. This may indicate a slow connection or large file size.`);
        }
        throw new Error(`PDF direct download failed after ${retries} attempts: ${error.message}`);
      }

      // Wait before retry
      const waitTime = 2000 * attempt; // 2s, 4s
      console.log(`[Preview] Waiting ${waitTime}ms before retry...`);
      await new Promise(resolve => setTimeout(resolve, waitTime));
    }
  }
};



/**
 * Ensure preview directories exist
 * @returns {void}
 */
const ensurePreviewDirectories = () => {
  try {
    const previewDir = './uploads/previews/';
    const tempDir = './temp/';

    if (!fs.existsSync(previewDir)) {
      console.log(`[Setup] Creating preview directory: ${previewDir}`);
      fs.mkdirSync(previewDir, { recursive: true });
    }

    if (!fs.existsSync(tempDir)) {
      console.log(`[Setup] Creating temp directory: ${tempDir}`);
      fs.mkdirSync(tempDir, { recursive: true });
    }
  } catch (error) {
    console.error('[Setup] Error creating directories:', error);
  }
};

module.exports = {
  generatePreview,
  generateVideoPreview,
  generatePdfPreview,
  canGeneratePreview,
  cleanupPreviewFile,
  ensurePreviewDirectories,
  validateAndExtractS3Key
};
